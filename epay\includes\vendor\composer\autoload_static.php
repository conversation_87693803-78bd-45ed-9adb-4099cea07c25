<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInita012aca486d6abc048243f4697c6ac40
{
    public static $files = array (
        '382a2ac8aeff4e600f8a2b1256c841e2' => __DIR__ . '/..' . '/lpilp/guomi/src/overwrite.php',
    );

    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'WeChatPay\\' => 10,
        ),
        'R' => 
        array (
            'Rtgm\\' => 5,
        ),
        'Q' => 
        array (
            'QQPay\\' => 6,
        ),
        'M' => 
        array (
            '<PERSON><PERSON>er\\Ecc\\' => 12,
        ),
        'F' => 
        array (
            'FG\\' => 3,
        ),
        'A' => 
        array (
            'Alipay\\' => 7,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'WeChatPay\\' => 
        array (
            0 => __DIR__ . '/..' . '/cccyun/wechatpay-sdk/src',
        ),
        'Rtgm\\' => 
        array (
            0 => __DIR__ . '/..' . '/lpilp/guomi/src',
        ),
        'QQPay\\' => 
        array (
            0 => __DIR__ . '/..' . '/cccyun/qqpay-sdk/src',
        ),
        'Mdanter\\Ecc\\' => 
        array (
            0 => __DIR__ . '/..' . '/mdanter/ecc/src',
        ),
        'FG\\' => 
        array (
            0 => __DIR__ . '/..' . '/fgrosse/phpasn1/lib',
        ),
        'Alipay\\' => 
        array (
            0 => __DIR__ . '/..' . '/cccyun/alipay-sdk/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInita012aca486d6abc048243f4697c6ac40::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInita012aca486d6abc048243f4697c6ac40::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInita012aca486d6abc048243f4697c6ac40::$classMap;

        }, null, ClassLoader::class);
    }
}
