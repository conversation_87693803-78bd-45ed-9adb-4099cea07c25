<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Material 3 Theme for Android 12+ with dynamic color support -->
    <style name="Theme.VMQ" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary colors -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        
        <!-- Secondary colors -->
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        
        <!-- Tertiary colors -->
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        
        <!-- Error colors -->
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        
        <!-- Surface variant colors -->
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_light_inversePrimary</item>
        
        <!-- Window attributes -->
        <item name="android:windowBackground">?attr/colorSurface</item>
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowActivityTransitions">true</item>
        
        <!-- Android 12+ Splash Screen API - 优化配置 -->
        <item name="android:windowSplashScreenBackground">?attr/colorSurface</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/ic_splash_screen</item>
        <item name="android:windowSplashScreenAnimationDuration">800</item>
        <item name="android:windowSplashScreenIconBackgroundColor">@android:color/transparent</item>
        <item name="android:windowSplashScreenBrandingImage">@null</item>
        
        <!-- Dynamic colors handled programmatically -->
    </style>
    
    <!-- 深色主题的启动画面配置 -->
    <style name="Theme.VMQ.Dark" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- 深色主题颜色配置 -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        
        <!-- 深色模式下的启动画面配置 -->
        <item name="android:windowSplashScreenBackground">?attr/colorSurface</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/ic_splash_screen</item>
        <item name="android:windowSplashScreenAnimationDuration">800</item>
        <item name="android:windowSplashScreenIconBackgroundColor">@android:color/transparent</item>
        <item name="android:windowSplashScreenBrandingImage">@null</item>
    </style>
</resources>