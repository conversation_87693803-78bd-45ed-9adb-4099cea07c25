<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    android:keepScreenOn="true"
    tools:context="com.vone.vmq.LockShowActivity">

    <TextView
        android:id="@+id/showTv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:layout_marginStart="40dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="40dp"
        android:layout_marginBottom="20dp"
        android:background="#00000000"
        android:gravity="center"
        android:text="@string/is_post_server_tip"
        android:textColor="#FFF"
        android:textSize="16sp" />

</RelativeLayout>