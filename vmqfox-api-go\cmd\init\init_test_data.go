package main

import (
	"fmt"
	"log"

	"vmqfox-api-go/internal/config"
	"vmqfox-api-go/internal/model"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🔧 初始化VMQFox测试数据...")

	// 加载配置
	err := config.LoadConfig(".")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 生成数据库连接字符串
	cfg := &config.AppConfig.Database
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local&timeout=10s&readTimeout=30s&writeTimeout=30s",
		cfg.Username,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Database,
		cfg.Charset,
	)

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 初始化测试设置
	settings := []model.Setting{
		{Vkey: "key", Vvalue: "test_secret_key", User_id: 1},
		{Vkey: "jkstate", Vvalue: "1", User_id: 1},
		{Vkey: "close", Vvalue: "5", User_id: 1},
		{Vkey: "frontend_url", Vvalue: "http://localhost:3000", User_id: 1},
		{Vkey: "notifyUrl", Vvalue: "http://example.com/notify", User_id: 1},
		{Vkey: "returnUrl", Vvalue: "http://example.com/return", User_id: 1},
		{Vkey: "wxpay", Vvalue: "wxp://f2f0test123456", User_id: 1},
		{Vkey: "zfbpay", Vvalue: "https://qr.alipay.com/test123456", User_id: 1},
	}

	for _, setting := range settings {
		// 使用 GORM 的 Save 方法，如果存在则更新，不存在则创建
		var existingSetting model.Setting
		result := db.Where("vkey = ? AND user_id = ?", setting.Vkey, setting.User_id).First(&existingSetting)

		if result.Error == gorm.ErrRecordNotFound {
			// 记录不存在，创建新记录
			if err := db.Create(&setting).Error; err != nil {
				log.Printf("❌ 创建设置失败 %s: %v", setting.Vkey, err)
			} else {
				fmt.Printf("✅ 创建设置: %s = %s\n", setting.Vkey, setting.Vvalue)
			}
		} else if result.Error == nil {
			// 记录存在，更新值
			if err := db.Model(&existingSetting).Update("vvalue", setting.Vvalue).Error; err != nil {
				log.Printf("❌ 更新设置失败 %s: %v", setting.Vkey, err)
			} else {
				fmt.Printf("✅ 更新设置: %s = %s\n", setting.Vkey, setting.Vvalue)
			}
		} else {
			log.Printf("❌ 查询设置失败 %s: %v", setting.Vkey, result.Error)
		}
	}

	// 验证设置
	fmt.Println("\n📋 当前设置:")
	var allSettings []model.Setting
	db.Where("user_id = ?", 1).Find(&allSettings)
	for _, setting := range allSettings {
		fmt.Printf("   %s = %s\n", setting.Vkey, setting.Vvalue)
	}

	fmt.Println("\n🎉 测试数据初始化完成！")
}
