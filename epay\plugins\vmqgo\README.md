# VMQ Go多用户版支付插件配置说明

## 版本信息
- **插件版本**: 2.0.0
- **适配版本**: VMQ Go多用户版
- **更新日期**: 2025-07-23

## 功能特性

### 🎯 多用户支持
- 支持通过商户ID(AppID)自动识别用户身份
- 每个用户拥有独立的配置和订单管理
- 支持用户级别的订单过期时间设置
- 支持用户级别的监控状态管理

### 🔐 安全机制
- 基于MD5的签名验证机制
- 支持商户ID参与签名计算
- 防重放攻击保护
- 完整的回调验证流程

## 配置步骤

### 第一步：获取VMQ配置信息

1. 登录VMQ管理后台 (默认: http://localhost:3007)
2. 进入"系统设置"页面
3. 记录以下信息：
   - **商户ID(AppID)**: 例如 `VMQ_6E094E2D4346`
   - **通讯密钥**: 例如 `test_secret_key`
   - **接口地址**: 例如 `http://localhost:8000/`

### 第二步：配置epay插件

1. 登录epay管理后台
2. 进入"支付接口"管理
3. 添加或编辑"V免签Go多用户版"插件
4. 填入配置信息：

```
接口地址: http://localhost:8000/
商户ID(AppID): VMQ_6E094E2D4346
通讯密钥: test_secret_key
```

**注意事项：**
- 接口地址必须以 `/` 结尾
- 商户ID必须与VMQ系统中的AppID完全一致
- 通讯密钥必须与VMQ系统中的密钥完全一致

### 第三步：测试配置

1. 在epay中创建测试订单
2. 选择VMQ支付方式
3. 检查订单是否正确跳转到VMQ支付页面
4. 完成支付后检查回调是否正常

## 工作原理

### 订单创建流程

```
epay商户平台 → VMQ Go API → 用户识别 → 订单创建
     ↓              ↓           ↓          ↓
  发送AppID    接收AppID    查找用户    绑定用户
```

1. **请求发送**: epay将AppID作为param参数发送给VMQ
2. **用户识别**: VMQ通过AppID在merchant_mapping表中查找对应的user_id
3. **签名验证**: VMQ使用对应用户的密钥验证签名
4. **订单创建**: 订单自动绑定到识别出的用户账户

### 签名算法

**创建订单签名:**
```php
$sign = md5('payId='.$payId.'&param='.$param.'&type='.$type.'&price='.$price.'&key='.$key);
```

**回调签名:**
```php
$sign = md5($payId . $param . $type . $price . $reallyPrice . $key);
```

## 多用户优势

### 🏢 商户隔离
- 每个商户使用独立的AppID
- 订单数据完全隔离
- 配置互不影响

### ⚙️ 个性化配置
- 独立的订单过期时间设置
- 独立的监控状态管理
- 独立的收款码配置

### 📊 数据统计
- 按用户统计订单数据
- 独立的收款统计
- 用户级别的数据分析

## 故障排除

### 常见问题

**1. 签名验证失败**
- 检查AppID是否正确
- 检查通讯密钥是否正确
- 确认VMQ中该用户的配置

**2. 用户识别失败**
- 检查AppID格式是否正确
- 确认merchant_mapping表中是否有对应记录
- 检查用户状态是否正常

**3. 订单创建失败**
- 检查VMQ服务是否正常运行
- 检查用户的监控状态是否开启
- 检查网络连接是否正常

### 调试方法

1. **查看VMQ日志**:
   ```bash
   # 查看Go服务日志
   tail -f vmqfox-api-go/logs/app.log
   ```

2. **检查数据库**:
   ```sql
   -- 检查商户映射
   SELECT * FROM merchant_mapping WHERE app_id = 'YOUR_APP_ID';
   
   -- 检查用户设置
   SELECT * FROM setting WHERE user_id = YOUR_USER_ID;
   ```

3. **测试API**:
   ```bash
   # 测试订单创建
   curl -X POST http://localhost:8000/api/public/order \
     -H "Content-Type: application/json" \
     -d '{"payId":"TEST001","param":"VMQ_6E094E2D4346","type":1,"price":1,"sign":"YOUR_SIGN"}'
   ```

## 技术支持

如遇到问题，请提供以下信息：
- VMQ版本信息
- 插件配置截图
- 错误日志内容
- 测试订单详情

---

**更新记录:**
- 2025-07-23: 适配VMQ Go多用户版本，支持AppID用户识别
- 2024-xx-xx: 初始版本发布
