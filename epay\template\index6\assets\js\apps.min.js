/*
Template Name: Color Admin - Responsive Admin Dashboard Template build with Twitter Bootstrap 3.3.7
Version: 2.1.0
Author: <PERSON>
Website: http://www.seantheme.com/color-admin-v2.1/frontend/one-page-parallax/
*/var handleHomeContentHeight=function(){$("#home").height($(window).height())},handleHeaderNavigationState=function(){$(window).on("scroll load",function(){if("disabled"!=$("#header").attr("data-state-change")){var e=$(window).scrollTop(),t=$("#header").height();e>=t?$("#header").addClass("navbar-small"):$("#header").removeClass("navbar-small")}})},handleAddCommasToNumber=function(e){return e.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g,"$1,")},handlePageContainerShow=function(){$("#page-container").addClass("in")},handlePaceLoadingPlugins=function(){Pace.on("hide",function(){$(".pace").addClass("hide")})},handlePageScrollContentAnimation=function(){$('[data-scrollview="true"]').each(function(){var e=$(this),t=scrollMonitor.create(e,60);t.enterViewport(function(){$(e).find("[data-animation=true]").each(function(){var e=$(this).attr("data-animation-type"),t=$(this);if(!$(t).hasClass("contentAnimated"))if("number"==e){var a=parseInt($(t).attr("data-final-number"));$({animateNumber:0}).animate({animateNumber:a},{duration:1e3,easing:"swing",step:function(){var e=handleAddCommasToNumber(Math.ceil(this.animateNumber));$(t).text(e).addClass("contentAnimated")}})}else $(this).addClass(e+" contentAnimated"),setTimeout(function(){$(t).addClass("finishAnimated")},1500)})})})},handleHeaderScrollToAction=function(){$("[data-click=scroll-to-target]").on("click",function(e){e.preventDefault(),e.stopPropagation();var t=$(this).attr("href"),a=50;if($("html, body").animate({scrollTop:$(t).offset().top-a},500),"dropdown"==$(this).attr("data-toggle")){var n=$(this).closest("li.dropdown");$(n).hasClass("open")?$(n).removeClass("open"):$(n).addClass("open")}}),$(document).click(function(e){e.isPropagationStopped()||$(".dropdown.open").removeClass("open")})},handleTooltipActivation=function(){0!==$("[data-toggle=tooltip]").length&&$("[data-toggle=tooltip]").tooltip()},handleThemePanelExpand=function(){$('[data-click="theme-panel-expand"]').live("click",function(){var e=".theme-panel",t="active";$(e).hasClass(t)?$(e).removeClass(t):$(e).addClass(t)})},handleThemePageControl=function(){if($.cookie&&$.cookie("theme")){0!==$(".theme-list").length&&($(".theme-list [data-theme]").closest("li").removeClass("active"),$('.theme-list [data-theme="'+$.cookie("theme")+'"]').closest("li").addClass("active"));var e=staticroot+"css/theme/"+$.cookie("theme")+".css";$("#theme").attr("href",e)}$(".theme-list [data-theme]").live("click",function(){var e=staticroot+"css/theme/"+$(this).attr("data-theme")+".css";$("#theme").attr("href",e),$(".theme-list [data-theme]").not(this).closest("li").removeClass("active"),$(this).closest("li").addClass("active"),$.cookie("theme",$(this).attr("data-theme"))})},App=function(){"use strict";return{init:function(){handleHomeContentHeight(),handleHeaderNavigationState(),handlePageContainerShow(),handlePaceLoadingPlugins(),handlePageScrollContentAnimation(),handleHeaderScrollToAction(),handleTooltipActivation(),handleThemePanelExpand(),handleThemePageControl()}}}();