<?php

/**
 * VMQ Go多用户版支付插件
 *
 * 版本: 2.0.0
 * 适配: VMQ Go多用户版本
 * 更新: 2025-07-23
 *
 * 使用说明:
 * 1. 在VMQ系统设置中获取商户ID(AppID)和通讯密钥
 * 2. 在epay后台配置此插件时填入对应的AppID和密钥
 * 3. VMQ会通过AppID自动识别用户身份并使用对应用户的配置
 *
 * 多用户支持:
 * - 每个用户都有独立的AppID和密钥
 * - 订单会自动绑定到对应的用户账户
 * - 支持用户级别的订单过期时间和监控状态配置
 */

class vmqgo_plugin
{
	static public $info = [
		'name'        => 'vmqgo', //支付插件英文名称，需和目录名称一致，不能有重复
		'showname'    => 'V免签Go多用户版', //支付插件显示名称
		'author'      => 'V免签go', //支付插件作者
		'link'        => 'https://github.com/szvone/vmqphp', //支付插件作者链接
		'types'       => ['alipay','qqpay','wxpay'], //支付插件支持的支付方式，可选的有alipay,qqpay,wxpay,bank
		'inputs' => [ //支付插件要求传入的参数以及参数显示名称，可选的有appid,appkey,appsecret,appurl,appmchid
			'appurl' => [
				'name' => '接口地址',
				'type' => 'input',
				'note' => '必须以http://或https://开头，以/结尾',
			],
			'appid' => [
				'name' => '商户ID(AppID)',
				'type' => 'input',
				'note' => '请填写VMQ系统设置中的商户ID，用于识别用户身份',
			],
			'appkey' => [
				'name' => '通讯密钥',
				'type' => 'input',
				'note' => '请填写VMQ系统设置中的通讯密钥',
			],
		],
		'select' => null,
		'note' => '适配VMQ Go多用户版本，支持通过商户ID自动识别用户身份。请在VMQ系统设置中获取商户ID和通讯密钥。', //支付密钥填写说明
		'bindwxmp' => false, //是否支持绑定微信公众号
		'bindwxa' => false, //是否支持绑定微信小程序
	];

	static public function submit(){
		global $siteurl, $channel, $order, $ordername, $sitename, $conf;

		if($order['typename']=='alipay'){
			$paytype='2';
		}elseif($order['typename']=='qqpay'){
			$paytype='4';
		}elseif($order['typename']=='wxpay'){
			$paytype='1';
		}elseif($order['typename']=='bank'){
			$paytype='3';
		}
		
		$apiurl = $channel['appurl'].'api/public/order';
		$data = array(
			"payId" => TRADE_NO,            // 商户订单号
			"type" => $paytype,             // 支付类型
			"price" => $order['realmoney'], // 订单金额
			"isHtml" => '1',                // 返回HTML而非JSON
			"notifyUrl" => $conf['localurl'].'pay/notify/'.TRADE_NO.'/',
			"returnUrl" => $siteurl.'pay/return/'.TRADE_NO.'/',
			"param" => $channel['appid'],   // 商户ID(AppID)，VMQ通过此字段识别用户身份
		);
		
		// VMQ Go多用户版签名规则: payId + param + type + price + key
		// param字段传递商户ID(AppID)，VMQ后端通过AppID识别用户并使用对应用户的密钥验证签名
		$param = empty($data['param']) ? '' : $data['param'];
		$data["sign"] = md5('payId='.$data['payId'].'&param='.$param.'&type='.$data['type'].'&price='.$data['price'].'&key='.$channel['appkey']);

        if ((function_exists('is_https') && is_https()) && substr($apiurl, 0, 7)=='http://') {
			$jump_url = $apiurl.'?'.http_build_query($data);
			return ['type'=>'jump','url'=>$jump_url];
        }else{
			$html_text = '<form action="'.$apiurl.'" method="post" id="dopay">';
			foreach($data as $k => $v) {
				$html_text .= "<input type=\"hidden\" name=\"{$k}\" value=\"{$v}\" />\n";
			}
			$html_text .= '<input type="submit" value="正在跳转"></form><script>document.getElementById("dopay").submit();</script>';

			return ['type'=>'html','data'=>$html_text];
		}
	}

	//异步回调 - VMQ Go多用户版
	static public function notify(){
		global $channel, $order;

		$payId = $_GET['payId'];//商户订单号
		$type = $_GET['type'];//支付方式 ：微信支付为1 支付宝支付为2 中国银联（云闪付）传入3 QQ钱包传入4
		$price = $_GET['price'];//订单金额
		$reallyPrice = $_GET['reallyPrice'];//实际支付金额
		$sign = $_GET['sign'];//校验签名，计算方式 = md5(payId + param + type + price + reallyPrice + 通讯密钥)

		if(!$payId || !$sign)return ['type'=>'html','data'=>'error_param'];

		// VMQ Go多用户版回调签名验证
		// param字段包含商户ID，用于多用户身份识别
		$param = isset($_GET['param']) ? $_GET['param'] : '';
		$_sign =  md5($payId . $param . $type . $price . $reallyPrice . $channel['appkey']);
		if ($_sign !== $sign)return ['type'=>'html','data'=>'error_sign'];

		$out_trade_no = daddslashes($payId);
		if($out_trade_no == TRADE_NO && round($price,2)==round($order['realmoney'],2)){
			processNotify($order, $out_trade_no);
		}
		return ['type'=>'html','data'=>'success'];
	}

	//同步回调 - VMQ Go多用户版
	static public function return(){
		global $channel, $order;

		$payId = $_GET['payId'];//商户订单号
		$type = $_GET['type'];//支付方式 ：微信支付为1 支付宝支付为2 中国银联（云闪付）传入3 QQ钱包传入4
		$price = $_GET['price'];//订单金额
		$reallyPrice = $_GET['reallyPrice'];//实际支付金额
		$sign = $_GET['sign'];//校验签名，计算方式 = md5(payId + param + type + price + reallyPrice + 通讯密钥)

		if(!$payId || !$sign)return ['type'=>'error','data'=>'参数不完整'];

		// VMQ Go多用户版回调签名验证
		// param字段包含商户ID，确保回调数据的完整性和安全性
		$param = isset($_GET['param']) ? $_GET['param'] : '';
		$_sign =  md5($payId . $param . $type . $price . $reallyPrice . $channel['appkey']);
		if ($_sign !== $sign)return ['type'=>'error','data'=>'签名校验失败'];

		$out_trade_no = daddslashes($payId);
		if($out_trade_no == TRADE_NO && round($price,2)==round($order['realmoney'],2)){
			processReturn($order, $out_trade_no);
		}else{
			return ['type'=>'error','msg'=>'订单信息校验失败'];
		}
	}

}