<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/toolbar"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="?attr/colorPrimary"
    app:contentInsetStart="0dip">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="40dip"
            android:layout_height="40dip"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackground"
            android:padding="10dip"
            android:scaleType="centerCrop"
            android:src="@drawable/btn_back" />

        <TextView
            android:id="@+id/txt_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="扫描二维码"
            android:textColor="@android:color/white"
            android:textSize="18sp" />

        <Button
            android:id="@+id/btn_album"
            android:layout_width="40dip"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dip"
            android:background="?attr/selectableItemBackground"
            android:text="相册"
            android:textColor="@android:color/white" />
    </RelativeLayout>
</androidx.appcompat.widget.Toolbar>
