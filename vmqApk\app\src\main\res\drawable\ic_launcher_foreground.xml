<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
  <!-- Material You 风格的前景图标 -->
  <group android:scaleX="2.4"
      android:scaleY="2.4"
      android:translateX="25.2"
      android:translateY="25.2">
    <!-- 盾牌形状代表安全监控 -->
    <path
        android:fillColor="?attr/colorOnPrimary"
        android:pathData="M12,1L3,5v6c0,5.55 3.84,10.74 9,12 5.16,-1.26 9,-6.45 9,-12L21,5l-9,-4z"/>
    <!-- 对勾表示验证通过 -->
    <path
        android:fillColor="?attr/colorPrimary"
        android:pathData="M10,17l-4,-4 1.41,-1.41L10,14.17l6.59,-6.59L18,9l-8,8z"/>
  </group>
  <!-- 添加微妙的装饰元素 -->
  <group android:translateX="54" android:translateY="54">
    <path
        android:fillColor="?attr/colorOnPrimary"
        android:pathData="M-2,-2 L2,-2 L2,2 L-2,2 Z"
        android:fillAlpha="0.6"/>
  </group>
</vector> 