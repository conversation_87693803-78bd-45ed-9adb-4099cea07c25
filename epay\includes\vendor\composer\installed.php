<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '*******',
        'reference' => NULL,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '*******',
            'reference' => NULL,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cccyun/alipay-sdk' => array(
            'pretty_version' => '1.4',
            'version' => '*******',
            'reference' => 'c6299991107d66481ed151dae1cd66889cb11a9e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cccyun/alipay-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cccyun/qqpay-sdk' => array(
            'pretty_version' => '1.2',
            'version' => '*******',
            'reference' => '873e1d9f06f3cecdbad165fa921096437cc8bcbe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cccyun/qqpay-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cccyun/wechatpay-sdk' => array(
            'pretty_version' => '1.5',
            'version' => '*******',
            'reference' => '13bc59fe1aa47cd4d1bf8e96c7a259b3c53b036b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cccyun/wechatpay-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fgrosse/phpasn1' => array(
            'pretty_version' => 'v2.5.0',
            'version' => '*******',
            'reference' => '42060ed45344789fb9f21f9f1864fc47b9e3507b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fgrosse/phpasn1',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lpilp/guomi' => array(
            'pretty_version' => 'v1.0.8',
            'version' => '1.0.8.0',
            'reference' => '56cbe64c404178c4c427042682c0e86ef201134b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lpilp/guomi',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mdanter/ecc' => array(
            'pretty_version' => 'v1.0.0',
            'version' => '*******',
            'reference' => '34e2eec096bf3dcda814e8f66dd91ae87a2db7cd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mdanter/ecc',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
