	<rule name="payrule1_rewrite" stopProcessing="true">
		<match url="^(.[a-zA-Z0-9-_]+).html"/>
		<conditions logicalGrouping="MatchAll">
			<add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true"/>
			<add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true"/>
		</conditions>
		<action type="Rewrite" url="index.php?mod={R:1}"/>
	</rule>
	<rule name="payrule2_rewrite" stopProcessing="true">
		<match url="^pay/(.*)"/>
		<conditions logicalGrouping="MatchAll">
			<add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true"/>
			<add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true"/>
		</conditions>
		<action type="Rewrite" url="pay.php?s={R:1}"/>
	</rule>
