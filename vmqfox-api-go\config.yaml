server:
  port: "8000"
  mode: "debug"  # debug, release, test
  read_timeout: "30s"
  write_timeout: "30s"
  frontend_url: "http://localhost:3006"

database:
  driver: "mysql"
  host: "localhost"
  port: 3306
  username: "root"
  password: "hxc1314520"
  database: "vmqgo"
  charset: "utf8mb4"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: "1h"

jwt:
  secret: "vmqfox-super-secret-key-2024"
  access_token_ttl: "2h"
  refresh_token_ttl: "168h"  # 7 days
  issuer: "vmqfox"

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0

log:
  level: "info"
  filename: "logs/app.log"
  max_size: 100
  max_age: 30
  max_backups: 10
  compress: true
