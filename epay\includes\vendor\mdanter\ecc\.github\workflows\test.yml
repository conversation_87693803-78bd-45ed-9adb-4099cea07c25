name: Tests

on:
  push:
  pull_request:

jobs:
  test:
    runs-on: ${{ matrix.operating-system }}
    strategy:
      matrix:
        operating-system: ['ubuntu-20.04', 'ubuntu-18.04']
        php-versions: ['7.0', '7.1', '7.2', '7.3', '7.4', '8.0']
        phpunit-versions: ['latest']
    steps:
    - name: Checkout
      uses: actions/checkout@v2
    - name: Checkout submodules
      uses: textbook/git-checkout-submodule-action@master

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-versions }}
        extensions: gmp
        coverage: none

    - name: Get composer cache directory
      id: composer-cache
      run: echo "::set-output name=dir::$(composer config cache-files-dir)"

    - name: Cache dependencies
      uses: actions/cache@v2
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: ${{ runner.os }}-composer-

    - name: Install dependencies
      run: composer install --prefer-dist

    - name: Run tests
      run: make phpunit-full-ci

  coverage:
    runs-on: ${{ matrix.operating-system }}
    strategy:
      matrix:
        operating-system: ['ubuntu-20.04']
        php-versions: ['7.4']

    steps:
    - name: Checkout
      uses: actions/checkout@v2
    - name: Checkout submodules
      uses: textbook/git-checkout-submodule-action@master

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-versions }}
        extensions: gmp, xdebug

    - name: Get composer cache directory
      id: composer-cache
      run: echo "::set-output name=dir::$(composer config cache-files-dir)"

    - name: Cache dependencies
      uses: actions/cache@v2
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: ${{ runner.os }}-composer-

    - name: Install dependencies
      run: composer install --prefer-dist

    - name: Run tests
      run: make phpunit-ci

    - name: Upload coverage to Scrutinizer
      run: make scrutinizer
