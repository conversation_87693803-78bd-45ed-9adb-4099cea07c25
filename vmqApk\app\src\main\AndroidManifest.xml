<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" /> <!-- 网络权限 -->
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- 震动权限 -->
    <uses-permission android:name="android.permission.CAMERA" /> <!-- 摄像头权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" 
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.MANA<PERSON>_EXTERNAL_STORAGE" 
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-feature android:name="android.hardware.camera.autofocus" /> <!-- 自动聚焦权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!--  新增权限  -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <application
        android:name="com.vone.vmq.App"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/Theme.VMQ">
        <activity
            android:name="com.vone.vmq.MainActivity"
            android:exported="true"
            android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.google.zxing.activity.CaptureActivity"
            android:excludeFromRecents="true" />
        
        <activity
            android:name="com.vone.vmq.SplashScreenTestActivity"
            android:label="启动画面测试工具"
            android:excludeFromRecents="true"
            android:exported="false"
            android:enabled="${enableSplashTestTools}" />

        <service
            android:name="com.vone.vmq.NeNotificationService2"
            android:exported="true"
            android:label="@string/app_name"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE">
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>

        <service 
            android:name="com.vone.vmq.ForegroundServer"
            android:foregroundServiceType="dataSync" />

        <activity
            android:name="com.vone.vmq.LockShowActivity"
            android:excludeFromRecents="true"
            android:launchMode="singleInstance" />


        <receiver
            android:name="com.vone.vmq.StartReceive"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.provider.Telephony.SECRET_CODE" />

                <data
                    android:host="66469"
                    android:scheme="android_secret_code" />
            </intent-filter>
        </receiver>
    </application>

</manifest>