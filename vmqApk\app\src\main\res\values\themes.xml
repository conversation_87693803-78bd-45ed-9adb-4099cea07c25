<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Material 3 Light Theme -->
    <style name="Theme.VMQ" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary colors -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        
        <!-- Secondary colors -->
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        
        <!-- Tertiary colors -->
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        
        <!-- Error colors -->
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        
        <!-- Surface variant colors -->
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_light_inversePrimary</item>
        
        <!-- Window attributes for better startup experience -->
        <item name="android:windowBackground">@drawable/splash_screen_legacy</item>
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowActivityTransitions">true</item>
        <item name="android:windowAllowEnterTransitionOverlap">true</item>
        <item name="android:windowAllowReturnTransitionOverlap">true</item>
        
        <!-- 启动画面优化 -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">?attr/colorSurface</item>
        <item name="android:navigationBarColor">?attr/colorSurface</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="o_mr1">true</item>
        
        <!-- 防止启动时的白屏闪烁 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
        
        <!-- Dynamic colors handled programmatically -->
    </style>
    
    <!-- Material 3 Dynamic Color Theme for Android 12+ -->
    <style name="Theme.VMQ.DynamicColor" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Fallback colors when dynamic colors are not available -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        
        <item name="android:windowBackground">?attr/colorSurface</item>
        <item name="android:windowContentTransitions">true</item>
    </style>
    
    <!-- Legacy theme for backward compatibility -->
    <style name="AppTheme" parent="Theme.VMQ" />
    
    <!-- Custom button styles -->
    <style name="Widget.VMQ.Button" parent="Widget.Material3.Button">
        <item name="android:textAllCaps">false</item>
        <item name="android:minHeight">48dp</item>
        <item name="cornerRadius">12dp</item>
    </style>
    
    <style name="Widget.VMQ.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textAllCaps">false</item>
        <item name="android:minHeight">48dp</item>
        <item name="cornerRadius">12dp</item>
    </style>
    
    <style name="Widget.VMQ.Button.Text" parent="Widget.Material3.Button.TextButton">
        <item name="android:textAllCaps">false</item>
        <item name="android:minHeight">48dp</item>
    </style>
</resources>