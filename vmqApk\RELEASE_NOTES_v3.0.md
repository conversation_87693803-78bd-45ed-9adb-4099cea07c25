# V免签 v3.0 发布说明

## 🎉 重大更新：Material You 设计升级

### ✨ 新功能特性

#### 🎨 Material You 设计系统
- **动态颜色支持**：在 Android 12+ 设备上自动适配系统壁纸颜色
- **Material 3 主题**：全新的现代化界面设计
- **自适应图标**：支持 Android 12+ 的主题化图标
- **启动画面**：使用 Android 12+ 新的启动画面 API

#### 🔧 技术升级
- **Android 14 支持**：目标 SDK 升级至 34
- **最低版本要求**：Android 6.0 (API 23) 及以上
- **依赖库更新**：
  - Material Design Components 1.11.0
  - OkHttp 4.12.0
  - ZXing 3.5.2
  - AndroidX 库全面替换 Support Library

#### 🛡️ 权限和兼容性
- **Android 13+ 通知权限**：适配新的通知权限模型
- **前台服务优化**：符合最新的后台任务限制
- **存储权限适配**：支持 Android 11+ 的文件访问权限
- **相机权限优化**：改进二维码扫描权限处理

#### 🎯 用户体验改进
- **首次启动引导**：新用户友好的欢迎和权限设置指南
- **错误处理优化**：更清晰的错误提示和解决方案
- **性能监控**：内置性能监控，确保应用流畅运行
- **触觉反馈**：Material 3 风格的交互反馈

### 🔄 兼容性信息

#### 支持的 Android 版本
- **最低要求**：Android 6.0 (API 23)
- **目标版本**：Android 14 (API 34)
- **推荐版本**：Android 12+ (完整 Material You 体验)

#### 功能兼容性
| 功能 | Android 6-11 | Android 12+ |
|------|-------------|-------------|
| 基础功能 | ✅ 完全支持 | ✅ 完全支持 |
| Material 3 主题 | ✅ 静态主题 | ✅ 动态颜色 |
| 启动画面 | ✅ 传统方式 | ✅ 新 API |
| 通知权限 | ✅ 自动授权 | ⚠️ 需要手动授权 |
| 自适应图标 | ✅ 基础支持 | ✅ 主题化支持 |

### 🚀 性能优化

#### 启动性能
- 应用启动时间优化
- 内存使用监控和优化
- 网络请求性能改进

#### 稳定性提升
- 错误处理机制完善
- 兼容性检查和降级方案
- 用户引导和帮助系统

### 🛠️ 开发者改进

#### 代码质量
- 修复所有过时 API 使用
- 使用现代 Android 开发最佳实践
- 完善的测试覆盖

#### 架构优化
- Activity Result API 替换过时方法
- 改进的权限处理流程
- 模块化的主题系统

### 📋 升级指南

#### 从 v2.x 升级
1. **自动升级**：核心功能保持兼容，无需额外配置
2. **权限重新授权**：Android 13+ 设备需要重新授权通知权限
3. **界面适应**：新的 Material You 界面，操作方式保持不变

#### 首次安装
1. 按照应用内引导完成权限设置
2. 扫码或手动配置服务器信息
3. 确保应用加入电池优化白名单

### ⚠️ 重要提醒

#### 权限要求
- **通知访问权限**：监听支付通知（核心功能）
- **网络权限**：发送收款信息到服务器
- **存储权限**：保存配置和日志文件
- **相机权限**：扫描配置二维码

#### 设备要求
- Android 6.0 或更高版本
- 支持通知监听服务的设备
- 网络连接（Wi-Fi 或移动数据）

### 🐛 已知问题

#### 第三方库警告
- ZXing 库使用旧 Camera API（不影响功能）
- 部分厂商 ROM 可能需要额外权限设置

#### 兼容性注意
- 部分 Android 6.0 设备可能不支持完整 Material You 效果
- 动态颜色功能仅在 Android 12+ 设备上可用

### 📞 技术支持

如遇到问题，请：
1. 查看应用内日志输出
2. 检查权限设置是否完整
3. 确认网络连接正常
4. 重启应用或重新安装

---

**感谢使用 V免签！** 🎉

本次更新带来了全新的现代化体验，同时保持了原有功能的稳定性。我们致力于为用户提供最好的支付监控解决方案。