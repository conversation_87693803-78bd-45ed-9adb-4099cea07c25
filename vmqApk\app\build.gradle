apply plugin: 'com.android.application'

android {
    namespace 'com.vone.qrcode'
    compileSdk 34
    
    defaultConfig {
        applicationId "com.vone.qrcode"
        minSdk 23
        targetSdk 34
        versionCode 3
        versionName "3.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    
    buildFeatures {
        buildConfig true
    }
    
    signingConfigs {
        //加载资源
        Properties properties = new Properties()
        InputStream inputStream = project.rootProject.file('local.properties').newDataInputStream();
        properties.load(inputStream)

        signConfig {
            storeFile file(properties.getProperty("STORE_FILE_NAME"))//签名文件路径，
            storePassword properties.getProperty("KEY_PASSWORD") //密码
            keyAlias properties.getProperty("STORE_ALIAS")
            keyPassword properties.getProperty("KEY_PASSWORD")  //密码
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.signConfig // 配置debug包的签名,接入微信分享必须验证签名
            buildConfigField "boolean", "ENABLE_SPLASH_TEST_TOOLS", "true"
            manifestPlaceholders = [enableSplashTestTools: "true"]
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.signConfig
            buildConfigField "boolean", "ENABLE_SPLASH_TEST_TOOLS", "false"
            manifestPlaceholders = [enableSplashTestTools: "false"]
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    // 添加编译选项
    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:-options"
        options.encoding = "UTF-8"
        options.fork = true
        options.forkOptions.jvmArgs << "-Dfile.encoding=GBK"
    }
    
    lint {
        abortOnError false
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    androidTestImplementation 'androidx.test.espresso:espresso-contrib:3.5.1'
    androidTestImplementation 'androidx.test.espresso:espresso-intents:3.5.1'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'com.google.zxing:core:3.5.2'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okio:okio:3.6.0'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.core:core-splashscreen:1.0.1'
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.7.0'
    testImplementation 'org.robolectric:robolectric:4.11.1'
}
