@charset "utf-8";
/*联系我们-样式*/
.contact_comBox{
	width: 100%;
}
.contact_comBox .contactUs{
	width: 100%;
}
/*列表页-联系我们-开始*/
.contact_listBox{
	background: url(../images/map_bg.png) no-repeat left 450px/424px 450px;
	padding-bottom: 150px;
}
.contact_comBox .contactUs_content{
	margin-top: -300px;
}
.contact_comBox .contactUs .contactUs_listMap{
	width: 1200px;
	height: 600px;
	margin-top: 40px;
}
.iw_poi_title {color:#CC5522;font-size:14px;font-weight:bold;overflow:hidden;padding-right:13px;white-space:nowrap}
.iw_poi_content {font:12px arial,sans-serif;overflow:visible;padding-top:4px;white-space:-moz-pre-wrap;word-wrap:break-word}
.contact_comBox .contactUs .contactUs_listMap .listMap_mapBox{
	width: 1200px;
	height: 600px;
	border: 1px solid #CCCCCC;
}
/*列表页-联系我们-结束*/
.contact_comBox .contactUs_index{
	height: 572px;
	background: url(../images/contactus_bg.png) no-repeat left center/766px 572px;
	margin-top: 130px;
}
.contact_comBox .contactUs .contactUs_title{
	width: 100%;
	text-align: center;
	font-size: 30px;
}
.contact_comBox .contactUs .contactUs_indexTitle{
	color: #000000;
}
.contact_comBox .contactUs .contactUs_listTitle{
	color: #FFFFFF;
}
.contact_comBox .contactUs .contactUs_line{
	width: 60px;
	height: 3px;
	background: #d2d2d2;
	margin: 10px auto;
}
.contact_comBox .contactUs .contactUs_listTxt{
	width: 100%;
	text-align: center;
	font-size: 18px;
	color: #FFFFFF;
	padding: 10px 0;
}
.contact_comBox .contactUs .contactUs_main{
	width: 100%;
	overflow: hidden;
}
.contact_comBox .contactUs .contactUs_main li{
	width: 360px;
	height: 240px;
	background: #FFFFFF;
	border-radius: 7px;
	position: relative;
	-webkit-box-shadow: 0 3px 8px rgba(0,0,0,.1), 3px 0 8px rgba(0,0,0,.1), 0 4px 24px rgba(0,0,0,.05);
	box-shadow: 0 3px 8px rgba(0,0,0,.1), 3px 0 8px rgba(0,0,0,.1), 0 4px 24px rgba(0,0,0,.05);
	transition: all 0.3s;
}
.contact_comBox .contactUs .contactUs_main .contactUs_liMargin1{
	margin-left: 15px;
	margin-right: 15px;
	margin-top: 50px;
	margin-bottom: 20px;
}
.contact_comBox .contactUs .contactUs_main .contactUs_liMargin2{
	margin-left: 30px;
	margin-right: 30px;
	margin-top: 50px;
	margin-bottom: 20px;
}
.contact_comBox .contactUs .contactUs_main li .contactUs_icon{
	display: block;
	margin: 40px auto 20px auto;
}
.contact_comBox .contactUs .contactUs_main li .contactUs_icon1{
	width: 23px;
	height: 29px;
	background: url(../images/address1.png) no-repeat;
	background-size: 100%;
}
.contact_comBox .contactUs .contactUs_main li .contactUs_icon2{
	width: 31px;
	height: 26px;
	background: url(../images/tell.png) no-repeat;
	background-size: 100%;
}
.contact_comBox .contactUs .contactUs_main li .contactUs_icon3{
	width: 28px;
	height: 20px;
	background: url(../images/email.png) no-repeat;
	background-size: 100%;
}
.contact_comBox .contactUs .contactUs_main li:hover{
	margin-top: 28px;
}
.contact_comBox .contactUs .contactUs_main li:hover .contactUs_mainLine{
	background: #4e7dff;
}
.contact_comBox .contactUs .contactUs_main li:hover .contactUs_mainBot{
	display: block;
}
.contact_comBox .contactUs .contactUs_main li:hover .contactUs_icon1{
	background: url(../images/address.png) no-repeat;
}
.contact_comBox .contactUs .contactUs_main li:hover .contactUs_icon2{
	background: url(../images/tell1.png) no-repeat;
}
.contact_comBox .contactUs .contactUs_main li:hover .contactUs_icon3{
	background: url(../images/email1.png) no-repeat;
}
.contact_comBox .contactUs .contactUs_main li .contactUs_mainTit{
	display: block;
	width: 100%;
	text-align: center;
	font-size: 20px;
	color: #BBBBBB;
}
.contact_comBox .contactUs .contactUs_main li .contactUs_mainLine{
	display: block;
	width: 60px;
	margin: 15px auto 30px auto;
	height: 4px;
	background: #707070;
}
.contact_comBox .contactUs .contactUs_main li .contactUs_mainTxt{
	display: block;
	width: 100%;
	text-align: center;
	font-size: 18px;
	color: #909090;
}
.contact_comBox .contactUs .contactUs_main li .contactUs_mainBot{
	display: block;
	width: 100%;
	height: 8px;
	background: #4e7dff;
	position: absolute;
	bottom: 0;
	left: 0;
	display: none;
}